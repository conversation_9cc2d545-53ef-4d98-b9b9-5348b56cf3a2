<script lang="ts" setup>
import { watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { $post } from '@/utils/api'
import { md5Hash } from '@/utils/tool'
import { useGenerateImageVariant } from '@core/composable/useGenerateImageVariant'
import authV2LoginIllustrationDark from '@images/pages/auth-v2-login-illustration-dark.png'
import authV2LoginIllustrationLight from '@images/pages/auth-v2-login-illustration-light.png'
import authV2LoginIllustrationEn from '@images/pages/backgroundEn.png'
import { VNodeRenderer } from '@layouts/components/VNodeRenderer'
import { themeConfig } from '@themeConfig'

const router = useRouter()
const { locale } = useI18n()

// 检测浏览器语言并设置国际化语言
const detectBrowserLanguage = () => {
  const browserLang = navigator.language || navigator.languages?.[0] || 'en'
  const langCode = browserLang.toLowerCase()

  // 根据浏览器语言设置对应的locale
  if (langCode.startsWith('zh'))
    locale.value = 'cn'
  else
    locale.value = 'en'
}

let authThemeImg = reactive(useGenerateImageVariant(
  authV2LoginIllustrationLight,
  authV2LoginIllustrationDark))

watch(locale, newLocale => {
  console.log('Language changed to:', newLocale)
  if (newLocale === 'cn') {
    authThemeImg = useGenerateImageVariant(
      authV2LoginIllustrationLight,
      authV2LoginIllustrationDark)
  }

  if (newLocale === 'en') {
    authThemeImg = useGenerateImageVariant(
      authV2LoginIllustrationEn,
      authV2LoginIllustrationEn,
    )
  }
}, {
  immediate: true,
})

definePage({
  meta: {
    layout: 'blank',
    public: true,
  },
})

const form = ref({
  systemUserName: '',
  systemPassword: '',
  remember: false,
})

const isPasswordVisible = ref(false)

// const authThemeMask = useGenerateImageVariant(authV2MaskLight, authV2MaskDark)

const sendLogin = async () => {
  const { systemUserName, systemPassword, remember } = form.value

  const data: any = {
    username: systemUserName,
    password: md5Hash(systemPassword),
  }

  const res: any = await $post('/auth/signIn', data)

  console.log(res)

  if (res.msg === 'success') {
    sessionStorage.setItem('role', res.result.roleId)
    sessionStorage.setItem('token', res.result.token)
    localStorage.setItem('username', systemUserName)
    if (remember === true) {
      localStorage.setItem('password', systemPassword)
      localStorage.setItem('remember', '1')
    }
    else {
      localStorage.setItem('remember', '0')
    }
    router.push('/')
  }
}

const handleEnterKey = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    const { systemUserName, systemPassword } = form.value
    if (systemUserName && systemPassword)
      sendLogin()
  }
}

onMounted(() => {
  // 检测并设置浏览器语言
  detectBrowserLanguage()

  if (localStorage.getItem('remember') === '1') {
    form.value.systemUserName = localStorage.getItem('username') || 'admin'
    form.value.systemPassword = localStorage.getItem('password') || ''
    form.value.remember = true
  }
  else {
    form.value.remember = false
  }

  // 监听回车键
  window.addEventListener('keydown', handleEnterKey)
})

onUnmounted(() => {
  window.removeEventListener('keydown', handleEnterKey)
})
</script>

<template>
  <a href="javascript:void(0)">
    <div class="auth-logo d-flex align-center gap-x-3">
      <VNodeRenderer :nodes="themeConfig.app.logo" />
      <h1 class="auth-title">{{ themeConfig.app.title }}</h1>
    </div>
  </a>

  <VRow
    class="auth-wrapper bg-surface"
    no-gutters
  >
    <VCol
      class="d-none d-md-flex"
      md="8"
    >
      <div class="position-relative bg-background w-100 me-0">
        <div
          class="d-flex align-center justify-center w-100 h-100"
          style="padding-inline: 6.25rem;"
        >
          <VImg
            v-if="authThemeImg.length > 0"
            :src="authThemeImg"
            class="auth-illustration mt-16 mb-2"
            max-width="813"
          />
        </div>
      </div>
    </VCol>

    <VCol
      class="auth-card-v2 d-flex align-center justify-center"
      cols="12"
      md="4"
    >
      <VCard
        :width="750"
        class="mt-12 mt-sm-0 pa-6"
        flat
      >
        <VCardText>
          <h4 class="text-h4 mb-1">
            {{ $t('WelcomeMessage') }} <span class="text-capitalize">{{ $t('Someone') }} {{ $t('CommercialNetwork') }}</span> !
          </h4>
          <p class="mb-0">
            {{ $t('SimplifyComplexity') }}，{{ $t('EasyControl') }}
          </p>
        </VCardText>
        <VCardText>
          <VForm @submit.prevent="() => {}">
            <VRow>
              <!-- email -->
              <VCol cols="12">
                <AppTextField
                  v-model="form.systemUserName"
                  autofocus
                  :label="$t('Username')"
                  :placeholder="$t('Tip.EnterAccount')"
                  type="text"
                />
              </VCol>
              <!-- password -->
              <VCol cols="12">
                <AppTextField
                  v-model="form.systemPassword"
                  :append-inner-icon="isPasswordVisible ? 'tabler-eye-off' : 'tabler-eye'"
                  :type="isPasswordVisible ? 'text' : 'password'"
                  :label="$t('Password')"
                  :placeholder="$t('Tip.EnterPassword')"
                  @click:append-inner="isPasswordVisible = !isPasswordVisible"
                />
                <div class="d-flex align-center flex-wrap justify-space-between my-6">
                  <VCheckbox
                    v-model="form.remember"
                    :label="$t('RememberAccount')"
                  />
                  <span @click="router.push('/role/codeLogin')">验证码登录</span>
                </div>
                <VBtn
                  block
                  @click="sendLogin"
                >
                  {{ $t('Login') }}
                </VBtn>
              </VCol>

              <VCol
                class="d-flex align-center justify-center"
                cols="12"
              >
                <span class="mx-4">还没有账户？<span
                  class="text-primary"
                  @click="router.push('/role/register')"
                >立即注册</span></span>
              </VCol>
            </VRow>
          </VForm>
        </VCardText>
      </VCard>
    </VCol>
  </VRow>
</template>

<style lang="scss">
@use "@core/scss/template/pages/page-auth";

.v-theme--light {
  .bg-background {
    background: url("@images/pages/bg.png") no-repeat center center !important;
    background-attachment: fixed !important;
    background-size: cover !important;
  }
}

.v-theme--dark {
  .bg-background {
    background: #25293c !important;
  }
}
</style>
